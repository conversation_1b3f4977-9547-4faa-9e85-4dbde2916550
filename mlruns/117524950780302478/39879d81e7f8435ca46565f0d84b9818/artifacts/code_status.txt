On branch fa
Changes not staged for commit:
  (use "git add <file>..." to update what will be committed)
  (use "git restore <file>..." to discard changes in working directory)
	modified:   __pycache__/trading_strategy.cpython-310.pyc
	modified:   backtest_results/backtest_result.png
	modified:   backtest_results/metrics.csv
	modified:   backtest_results/portfolio_value.csv
	modified:   backtest_results/trade_records.csv
	modified:   mlruns/117524950780302478/4bf46662c72d4eab83121fb73b23f4fc/artifacts/portfolio_analysis
	modified:   quant_config.yaml
	modified:   trading_strategy.py

Untracked files:
  (use "git add <file>..." to include in what will be committed)
	mlruns/117524950780302478/39879d81e7f8435ca46565f0d84b9818/
	qlib/

no changes added to commit (use "git add" and/or "git commit -a")
