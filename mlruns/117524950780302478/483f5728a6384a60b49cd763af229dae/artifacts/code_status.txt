On branch new
Changes not staged for commit:
  (use "git add <file>..." to update what will be committed)
  (use "git restore <file>..." to discard changes in working directory)
	modified:   __pycache__/trading_strategy.cpython-310.pyc
	modified:   quant_config.yaml

Untracked files:
  (use "git add <file>..." to include in what will be committed)
	mlruns/117524950780302478/483f5728a6384a60b49cd763af229dae/
	mlruns/117524950780302478/4d866cbbe7b948958522a6a84c425370/
	qlib/

no changes added to commit (use "git add" and/or "git commit -a")
