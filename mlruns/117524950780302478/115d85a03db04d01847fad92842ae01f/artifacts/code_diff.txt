diff --git a/quant_config.yaml b/quant_config.yaml
index 3e9d8338..f5edf273 100644
--- a/quant_config.yaml
+++ b/quant_config.yaml
@@ -1,4 +1,7 @@
 # 量化工程配置文件
+# 全局随机种子设置，确保结果可复现
+random_seed: 42
+
 # 数据源配置
 qlib_init:
   provider_uri: ./qlib_data
@@ -51,6 +54,7 @@ lstm_model: &lstm_model
     early_stop: 10   # 增加早停轮数
     batch_size: 128  # 减小批量大小以提高泛化能力
     GPU: 0
+    seed: 42        # 添加固定随机种子，确保结果可复现
 
 # LightGBM模型配置
 lgb_model: &lgb_model
diff --git a/run_quant.py b/run_quant.py
index 5b5ce774..2ca0a48c 100644
--- a/run_quant.py
+++ b/run_quant.py
@@ -10,13 +10,47 @@ import traceback as tb
 import logging
 import pandas as pd
 import numpy as np
+import random
 
 # 设置日志级别
 logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
 logger = logging.getLogger(__name__)
 
+# 设置固定随机种子函数
+def set_seed(seed=42):
+    """
+    设置所有随机数生成器的种子，确保结果可复现
+
+    Parameters
+    ----------
+    seed : int, optional
+        随机种子值, by default 42
+    """
+    logger.info(f"设置固定随机种子: {seed}")
+    random.seed(seed)
+    np.random.seed(seed)
+    torch.manual_seed(seed)
+    if torch.cuda.is_available():
+        torch.cuda.manual_seed(seed)
+        torch.cuda.manual_seed_all(seed)
+        # 确保CUDA操作是确定性的
+        torch.backends.cudnn.deterministic = True
+        torch.backends.cudnn.benchmark = False
+    # 设置Python哈希种子
+    os.environ['PYTHONHASHSEED'] = str(seed)
+
 def run_workflow(config_path):
     try:
+        # 读取配置文件以获取随机种子
+        import yaml
+        with open(config_path, 'r') as f:
+            config = yaml.safe_load(f)
+
+        # 获取随机种子，如果配置中没有则使用默认值42
+        seed = config.get('random_seed', 42)
+        # 设置固定随机种子
+        set_seed(seed)
+
         # 设置PyTorch内存管理
         if torch.cuda.is_available():
             torch.cuda.empty_cache()
@@ -26,7 +60,7 @@ def run_workflow(config_path):
         # 使用多线程可以提高LSTM和模型融合的性能
         # 建议设置为物理核心数的一半到全部，避免过多线程竞争
         # 可以根据系统情况调整此值
-        cpu_threads = 4  # 可以根据您的CPU核心数调整
+        cpu_threads = 1  # 可以根据您的CPU核心数调整
         torch.set_num_threads(cpu_threads)
         logger.info(f"设置CPU线程数为{cpu_threads}")
 
@@ -168,6 +202,17 @@ if __name__ == "__main__":
     config_path = os.path.join(os.path.dirname(__file__), 'quant_config.yaml')
     logger.info(f"加载配置文件: {config_path}")
 
+    # 读取配置文件以获取随机种子
+    import yaml
+    with open(config_path, 'r') as f:
+        config = yaml.safe_load(f)
+
+    # 获取随机种子，如果配置中没有则使用默认值42
+    seed = config.get('random_seed', 42)
+    # 在主程序开始时就设置固定随机种子
+    set_seed(seed)
+    logger.info(f"主程序已设置固定随机种子: {seed}")
+
     # 2. 运行完整量化流程
     logger.info("开始运行量化交易流程")
     recorder = run_workflow(config_path)
