name: Test qlib from pip

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  build:
    timeout-minutes: 120

    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [windows-latest, ubuntu-24.04, ubuntu-22.04, macos-13, macos-14, macos-15]
        # In github action, using python 3.7, pip install will not match the latest version of the package.
        # Also, python 3.7 is no longer supported from macos-14, and will be phased out from macos-13 in the near future.
        # All things considered, we have removed python 3.7.
        python-version: ["3.8", "3.9", "3.10", "3.11", "3.12"]

    steps:
    - name: Test qlib from pip
      uses: actions/checkout@v3

    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}

    - name: Update pip to the latest version
      run: |
        python -m pip install --upgrade pip
      
    # Will cancel this step when the next qlib version is released. The current qlib version is: 0.9.6
    - name: Installing pywinpt for windows
      if: ${{ matrix.os == 'windows-latest' }}
      run: |
        python -m pip install pywinpty --only-binary=:all:

    # # joblib was released on 2025-05-04 with version 1.5.0, in which _backend_args was removed and replaced by _backend_kwargs.
    # This change caused the application to fail, so the version of joblib is restricted here. 
    # This restriction will be removed in the next release. The current qlib version is: 0.9.6
    - name: Qlib installation test
      run: |
        python -m pip install pyqlib
        python -m pip install "joblib<=1.4.2"

    # install.sh file contents from: https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh
    # brew_install.sh file contents from: https://raw.githubusercontent.com/Microsoft/qlib/main/.github/brew_install.sh
    - name: Install Lightgbm for MacOS
      if: ${{ matrix.os == 'macos-13' || matrix.os == 'macos-14' || matrix.os == 'macos-15' }}
      run: |
        /bin/bash -c "$(curl -fsSL https://github.com/SunsetWolf/qlib_dataset/releases/download/maocs_lightgbm/install.sh)"
        /bin/bash -c "$(curl -fsSL https://github.com/SunsetWolf/qlib_dataset/releases/download/maocs_lightgbm/brew_install.sh)"
        HOMEBREW_NO_AUTO_UPDATE=1 brew install lightgbm
        # FIX MacOS error: Segmentation fault
        # reference: https://github.com/microsoft/LightGBM/issues/4229
        wget https://raw.githubusercontent.com/Homebrew/homebrew-core/fb8323f2b170bd4ae97e1bac9bf3e2983af3fdb0/Formula/libomp.rb
        brew unlink libomp
        brew install libomp.rb

    - name: Downloads dependencies data
      run: |
        cd ..
        python -m qlib.run.get_data qlib_data --target_dir ~/.qlib/qlib_data/cn_data --region cn
        cd qlib

    - name: Test workflow by config
      run: |
        qrun examples/benchmarks/LightGBM/workflow_config_lightgbm_Alpha158.yaml
