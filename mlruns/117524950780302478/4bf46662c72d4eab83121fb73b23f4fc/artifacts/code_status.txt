On branch fa
Changes not staged for commit:
  (use "git add <file>..." to update what will be committed)
  (use "git restore <file>..." to discard changes in working directory)
	modified:   backtest_results/backtest_result.png
	modified:   backtest_results/metrics.csv
	modified:   backtest_results/portfolio_value.csv
	modified:   backtest_results/trade_records.csv
	modified:   quant_config.yaml

Untracked files:
  (use "git add <file>..." to include in what will be committed)
	mlruns/117524950780302478/4bf46662c72d4eab83121fb73b23f4fc/
	mlruns/117524950780302478/7fe8b8a23c874e8dbe2835cdffc05321/
	mlruns/117524950780302478/cd36586dbb9b4fbaaada3dd4723fafab/
	mlruns/117524950780302478/f2f2c3c398d44d1daeadac69f85a2f7c/
	qlib/

no changes added to commit (use "git add" and/or "git commit -a")
