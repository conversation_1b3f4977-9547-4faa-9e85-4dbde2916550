On branch new
Changes not staged for commit:
  (use "git add/rm <file>..." to update what will be committed)
  (use "git restore <file>..." to discard changes in working directory)
	modified:   __pycache__/trading_strategy.cpython-310.pyc
	modified:   backtest_results/backtest_result.png
	modified:   backtest_results/metrics.csv
	modified:   backtest_results/portfolio_value.csv
	modified:   backtest_results/trade_records.csv
	modified:   check_qlib_data.py
	modified:   kline_513980.sh_origin.csv
	modified:   qlib_data/calendars/5min.txt
	deleted:    qlib_data/calendars/day.txt
	modified:   qlib_data/features/513980/amount.5min.bin
	deleted:    qlib_data/features/513980/amount.day.bin
	modified:   qlib_data/features/513980/averageprice.5min.bin
	deleted:    qlib_data/features/513980/averageprice.day.bin
	modified:   qlib_data/features/513980/close.5min.bin
	deleted:    qlib_data/features/513980/close.day.bin
	modified:   qlib_data/features/513980/high.5min.bin
	deleted:    qlib_data/features/513980/high.day.bin
	modified:   qlib_data/features/513980/iopv.5min.bin
	deleted:    qlib_data/features/513980/iopv.day.bin
	modified:   qlib_data/features/513980/low.5min.bin
	deleted:    qlib_data/features/513980/low.day.bin
	modified:   qlib_data/features/513980/open.5min.bin
	deleted:    qlib_data/features/513980/open.day.bin
	modified:   qlib_data/features/513980/volume.5min.bin
	deleted:    qlib_data/features/513980/volume.day.bin
	modified:   qlib_data/instruments/all.txt
	modified:   quant_config.yaml

Untracked files:
  (use "git add <file>..." to include in what will be committed)
	m_513980.sh.csv
	merge_csv.py
	mlruns/117524950780302478/1e65ea801aff46f4833483c390a2f1d6/
	mlruns/117524950780302478/483f5728a6384a60b49cd763af229dae/
	mlruns/117524950780302478/4d866cbbe7b948958522a6a84c425370/
	prepare_qlib_data.py
	qlib/

no changes added to commit (use "git add" and/or "git commit -a")
