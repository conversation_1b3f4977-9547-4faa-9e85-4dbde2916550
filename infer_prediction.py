#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
推理预测脚本 - 生成固定值0作为预测结果并做出交易决策
"""

import qlib
import os
import gc
import traceback as tb
import logging
import pandas as pd
import yaml
from datetime import datetime
from trading_strategy import TradingStrategy

# 设置日志级别
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)  # 修改为INFO，显示更多日志信息

# 禁止matplotlib字体管理器的DEBUG日志输出
logging.getLogger('matplotlib.font_manager').setLevel(logging.WARNING)
logging.getLogger('matplotlib').setLevel(logging.WARNING)

# 不再需要这些函数，已删除：
# - detect_m_chip
# - get_optimal_threads
# - optimize_for_m_chip
# - set_seed
# - _continue_inference

def run_inference(config_path, segment="infer", **kwargs):
    """
    不再使用模型进行推理预测，直接返回固定值0

    Parameters
    ----------
    config_path : str
        配置文件路径
    segment : str
        要预测的数据段，默认为"infer"
    **kwargs : dict
        其他参数，为了保持与原函数签名兼容

    Returns
    -------
    pd.Series or pd.DataFrame
        包含固定值0的预测结果
    """
    try:
        logger.info("跳过实际推理，直接返回固定值0...")

        # 读取配置文件
        logger.info(f"加载配置文件: {config_path}")
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)

        # 初始化qlib以获取数据
        qlib_config = config.get('qlib_init', {})
        qlib.init(
            provider_uri=qlib_config.get('provider_uri', './qlib_data'),
            region=qlib_config.get('region', 'cn'),
            provider=qlib_config.get('provider', 'LocalProvider')
        )

        # 获取推理时间范围
        infer_start = config.get('task', {}).get('dataset', {}).get('kwargs', {}).get('segments', {}).get(segment, [None, None])[0]
        infer_end = config.get('task', {}).get('dataset', {}).get('kwargs', {}).get('segments', {}).get(segment, [None, None])[1]

        if infer_start is None or infer_end is None:
            logger.warning(f"无法从配置中获取{segment}时间范围，使用默认范围")
            infer_start = "2025-03-01"
            infer_end = "2025-03-31"

        # 获取股票代码
        instrument = "513980"

        # 获取时间范围内的所有时间点
        try:
            calendar = qlib.data.D.calendar(start_time=infer_start, end_time=infer_end, freq="5min")

            # 创建包含固定值0的预测结果
            pred_data = []
            for dt in calendar:
                pred_data.append((dt, instrument, 0.0))

            # 创建多级索引DataFrame
            pred = pd.DataFrame(pred_data, columns=["datetime", "instrument", "score"])
            pred.set_index(["datetime", "instrument"], inplace=True)

            logger.info(f"已创建包含固定值0的预测结果，形状: {pred.shape}")
            return pred

        except Exception as e:
            logger.error(f"创建预测结果时出错: {e}")
            logger.error(tb.format_exc())

            # 创建一个简单的预测结果
            pred = pd.Series([0.0], index=pd.MultiIndex.from_tuples([(pd.Timestamp(infer_start), instrument)], names=["datetime", "instrument"]))
            logger.info("已创建简单的预测结果")
            return pred

    except Exception as e:
        logger.error(f"创建固定值预测结果时发生错误: {e}")
        logger.error(tb.format_exc())

        # 返回一个最小的预测结果
        return pd.Series([0.0], index=pd.MultiIndex.from_tuples([(pd.Timestamp("2025-03-01"), "513980")], names=["datetime", "instrument"]))
    finally:
        # 手动触发垃圾回收
        gc.collect()
        logger.info("已执行内存回收")

def save_predictions_to_csv(pred, filename):
    """将预测结果保存为CSV文件"""
    try:
        if pred is None:
            logger.error("没有预测结果可保存")
            return

        if isinstance(pred, pd.Series):
            pred = pred.to_frame("score")

        # 确保目录存在
        os.makedirs(os.path.dirname(os.path.abspath(filename)), exist_ok=True)

        # 确保索引被保存
        pred.to_csv(filename)
        logger.info(f"预测结果已保存到: {filename}")
    except Exception as e:
        logger.error(f"保存预测结果到CSV时发生错误: {e}")
        logger.error(tb.format_exc())

def generate_trading_decisions(pred_df, instrument="513980", initial_capital=400000):
    """
    基于预测结果生成交易决策

    Parameters
    ----------
    pred_df : pd.DataFrame or pd.Series
        预测结果
    instrument : str, optional
        股票代码, by default "513980"
    initial_capital : float, optional
        初始资金, by default 400000

    Returns
    -------
    tuple
        (decisions_df, trade_records, portfolio_values)
        decisions_df: 包含交易决策的DataFrame
        trade_records: 交易记录列表
        portfolio_values: 投资组合价值列表
    """

    # 如果pred_df是Series，转换为DataFrame
    if isinstance(pred_df, pd.Series):
        pred_df = pred_df.to_frame("score")
        logger.info("将预测结果从Series转换为DataFrame")

    # 获取所有时间点
    try:
        all_times = sorted(pred_df.index.get_level_values('datetime').unique())
        logger.info(f"总时间点数量: {len(all_times)}")
    except:
        try:
            # 尝试获取第一级索引
            all_times = sorted(pred_df.index.get_level_values(0).unique())
            logger.info(f"使用第一级索引作为时间，总时间点数量: {len(all_times)}")
        except Exception as e:
            logger.error(f"无法获取时间索引: {e}")
            logger.error(tb.format_exc())
            return None

    # 设置基准ID
    benchmark_id = instrument

    # 获取股票价格数据
    try:
        start_time = all_times[0]
        end_time = all_times[-1]
        logger.info(f"获取价格数据，时间范围: {start_time} 到 {end_time}")

        price_data = qlib.data.D.features(
            instruments=[instrument],
            fields=["$close", "$open", "$high", "$low", "$volume"],
            start_time=start_time,
            end_time=end_time,
            freq="5min"
        )
        logger.info(f"获取到价格数据，形状: {price_data.shape}")
    except Exception as e:
        logger.error(f"获取价格数据失败: {e}")
        logger.error(tb.format_exc())
        return None

    # 使用封装的函数处理价格数据
    price_df = TradingStrategy.process_price_data(price_data, all_times, instrument)

    # 加载Alpha158特征数据
    alpha158_df = TradingStrategy.load_alpha158_features(price_df, instrument)

    # 计算技术指标
    price_df = TradingStrategy.calculate_technical_indicators(price_df, alpha158_df)

    # 初始化交易参数
    params = TradingStrategy.init_trading_params(initial_capital)

    # 交易决策记录
    trading_decisions = []

    # 遍历每个时间点进行交易决策
    for i, current_time in enumerate(price_df.index):
        # 处理单个交易步骤
        params, step_result = TradingStrategy.process_single_trading_step(
            params, i, current_time, pred_df, benchmark_id, price_df, alpha158_df
        )

        # 记录交易决策
        trading_decisions.append(step_result)

    # 转换交易决策为DataFrame
    decisions_df = pd.DataFrame(trading_decisions)
    if not decisions_df.empty:
        decisions_df.set_index("datetime", inplace=True)

    # 转换投资组合价值为DataFrame
    portfolio_df = pd.DataFrame(params["portfolio_values"])
    if not portfolio_df.empty:
        portfolio_df.set_index("datetime", inplace=True)

    # 转换交易记录为DataFrame
    trade_records_df = pd.DataFrame(params["trade_records"]) if params["trade_records"] else pd.DataFrame()

    return decisions_df, trade_records_df, portfolio_df

def save_trading_decisions_to_csv(decisions_df, filename):
    """将交易决策保存为CSV文件"""
    try:
        if decisions_df is None or decisions_df.empty:
            logger.error("没有交易决策可保存")
            return

        # 确保目录存在
        os.makedirs(os.path.dirname(os.path.abspath(filename)), exist_ok=True)

        # 保存交易决策
        decisions_df.to_csv(filename)
        logger.info(f"交易决策已保存到: {filename}")
    except Exception as e:
        logger.error(f"保存交易决策到CSV时发生错误: {e}")
        logger.error(tb.format_exc())

if __name__ == "__main__":
    # 添加直接输出到控制台
    print("=== 开始执行推理预测脚本 ===")

    # 获取配置文件路径
    config_path = os.path.join(os.path.dirname(__file__), 'quant_config.yaml')
    print(f"使用配置文件: {config_path}")

    # 设置优化参数
    use_cache = True  # 是否使用缓存

    # 使用固定的输出文件夹
    output_dir = "prediction_results"
    os.makedirs(output_dir, exist_ok=True)
    logger.info(f"使用输出文件夹: {output_dir}")
    print(f"输出将保存到: {output_dir}")

    # 使用简化的run_inference函数
    logger.info("开始创建固定值0预测结果...")
    print("开始创建固定值0预测结果...")
    start_time = datetime.now()
    pred = run_inference(config_path, segment="infer")
    end_time = datetime.now()
    duration = (end_time - start_time).total_seconds()
    msg = f"创建固定值预测结果完成，耗时: {duration:.2f}秒"
    logger.info(msg)
    print(msg)

    # 保存预测结果到CSV
    if pred is not None:
        print("成功生成预测结果，正在保存...")
        save_predictions_to_csv(pred, os.path.join(output_dir, "predictions_infer.csv"))

        # 生成交易决策
        msg = "开始基于预测结果生成交易决策..."
        logger.info(msg)
        print(msg)
        decisions_df, trade_records_df, portfolio_values_df = generate_trading_decisions(pred)

        # 保存交易决策和相关数据
        if decisions_df is not None:
            print("成功生成交易决策，正在保存相关数据...")
            decisions_file = os.path.join(output_dir, "trading_decisions.csv")
            save_trading_decisions_to_csv(decisions_df, decisions_file)

            # 统计买入和卖出决策
            buy_decisions = decisions_df[decisions_df["decision"] == "buy"]
            sell_decisions = decisions_df[decisions_df["decision"] == "sell"]
            msg = f"买入决策数量: {len(buy_decisions)}, 卖出决策数量: {len(sell_decisions)}"
            logger.info(msg)
            print(msg)

            # 保存交易记录
            if not trade_records_df.empty:
                trade_records_file = os.path.join(output_dir, "trade_records.csv")
                trade_records_df.to_csv(trade_records_file, index=False)
                logger.info(f"交易记录已保存到: {trade_records_file}")
                print(f"交易记录已保存到: {trade_records_file}")

                # 统计交易次数
                buy_trades = len(trade_records_df[trade_records_df["action"] == "买入"]) if "action" in trade_records_df.columns else 0
                sell_trades = len(trade_records_df[trade_records_df["action"].isin(["卖出", "常规卖出", "止盈", "止损", "跟踪止损"])]) if "action" in trade_records_df.columns else 0
                msg = f"交易次数: 买入 {buy_trades} 次, 卖出 {sell_trades} 次"
                logger.info(msg)
                print(msg)

            # 不保存投资组合价值，根据用户要求

            # 计算并显示最终收益指标
            if not portfolio_values_df.empty:
                # 计算收益率
                initial_value = portfolio_values_df["portfolio_value"].iloc[0]
                final_value = portfolio_values_df["portfolio_value"].iloc[-1]
                total_return = (final_value - initial_value) / initial_value * 100

                # 输出收益指标
                print("\n=== 最终收益 ===")
                print(f"初始资金: {initial_value:.2f}")
                print(f"最终价值: {final_value:.2f}")
                print(f"总收益: {final_value - initial_value:.2f}")
                print(f"总收益率: {total_return:.2f}%")

                # 记录到日志，使用一条简洁的消息
                logger.info(f"最终收益: 初始资金={initial_value:.2f}, 最终价值={final_value:.2f}, 总收益={final_value - initial_value:.2f}, 总收益率={total_return:.2f}%")

                # 保存收益指标到文件
                metrics = {
                    "初始资金": initial_value,
                    "最终价值": final_value,
                    "总收益": final_value - initial_value,
                    "总收益率": total_return,
                    "买入次数": buy_trades,
                    "卖出次数": sell_trades
                }
                metrics_file = os.path.join(output_dir, "performance_metrics.csv")
                pd.DataFrame([metrics]).to_csv(metrics_file, index=False)
                logger.info(f"收益指标已保存到: {metrics_file}")
                print(f"收益指标已保存到: {metrics_file}")

                # 输出最后一个交易决策
                if not decisions_df.empty:
                    last_decision = decisions_df.iloc[-1]
                    # 只使用print输出到控制台，避免重复
                    print("\n=== 最后一个交易决策 ===")
                    print(f"时间: {last_decision.name}")
                    print(f"决策: {last_decision['decision']}")
                    print(f"原因: {last_decision['reason']}")
                    print(f"价格: {last_decision['price']}")
                    print(f"技术得分: {last_decision['tech_score']}")
                    print(f"市场状态: {last_decision['market_state']}")
                    print(f"投资组合价值: {last_decision['portfolio_value']:.2f}")
                    print(f"现金: {last_decision['cash']:.2f}")
                    print(f"持股数量: {last_decision['shares']}")
                    # 只记录一条简洁的日志，避免日志文件过大
                    logger.info(f"最后一个交易决策: {last_decision['decision'].upper()} @ {last_decision['price']}, 技术得分: {last_decision['tech_score']}, 市场状态: {last_decision['market_state']}")
    else:
        print("未能生成预测结果，请检查日志以获取更多信息")

    logger.info(f"M4优化的推理预测、交易决策生成和收益计算完成，所有输出文件已保存到: {output_dir}")
    print(f"\n=== 脚本执行完成 ===\n所有输出文件已保存到: {output_dir}")