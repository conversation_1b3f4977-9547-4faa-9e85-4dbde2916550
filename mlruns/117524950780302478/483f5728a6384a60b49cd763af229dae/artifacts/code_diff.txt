diff --git a/__pycache__/trading_strategy.cpython-310.pyc b/__pycache__/trading_strategy.cpython-310.pyc
index a10d25bc..8c052127 100644
Binary files a/__pycache__/trading_strategy.cpython-310.pyc and b/__pycache__/trading_strategy.cpython-310.pyc differ
diff --git a/quant_config.yaml b/quant_config.yaml
index f5edf273..96876efc 100644
--- a/quant_config.yaml
+++ b/quant_config.yaml
@@ -30,7 +30,7 @@ port_analysis_config: &port_analysis_config
   # 回测时间范围配置
   backtest_config:
     start_time: "2025-03-01 09:30:00"
-    end_time: "2025-03-31 15:00:00"  # 与test段的结束时间一致
+    end_time: "2025-04-15 15:00:00"  # 与test段的结束时间一致
   # 基准指数配置
   benchmark_config:
     benchmark: *benchmark
@@ -171,7 +171,7 @@ task:
       segments:
         train: [2024-03-15, 2024-12-31]
         valid: [2025-01-01, 2025-01-31]
-        test:  [2025-03-01, 2025-03-31]
+        test:  [2025-03-01, 2025-04-15]
         infer: [2025-04-01, 2025-12-31]
   record:
     - class: SignalRecord
